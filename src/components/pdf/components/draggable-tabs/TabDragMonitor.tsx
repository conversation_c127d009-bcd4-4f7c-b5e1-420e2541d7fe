import {usePdfStore} from '@/store/pdf-store';
import React, {useEffect, useRef} from 'react';
import {useDragLayer} from 'react-dnd';
import {DragItem, DragTypes} from './types';
import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";
import {CloseOutlined} from "@ant-design/icons";

export const TabDragMonitor = ({}) => {
    const createdWindowIdRef = useRef<string | null>(null);
    const {
        createWindow,
        updateWindow,
        closeWindow,
        removeTabFromWindow,
        setDragTabWindowId,
        setDragTabSourceWindowId,
        setDragHoverWindowId,
        setTabItems,
        setActiveAid
    } = usePdfStore((state) => ({
        createWindow: state.createWindow,
        updateWindow: state.updateWindow,
        closeWindow: state.closeWindow,
        removeTabFromWindow: state.removeTabFromWindow,
        setDragTabWindowId: state.setDragTabWindowId,
        setDragTabSourceWindowId: state.setDragTabSourceWindowId,
        setDragHoverWindowId: state.setDragHoverWindowId,
        setTabItems: state.setTabItems,
        setActiveAid: state.setActiveAid,
    }));

    const {
        isDragging,
        item,
        currentOffset,
    } = useDragLayer((monitor) => ({
        isDragging: monitor.isDragging(),
        item: monitor.getItem() as DragItem,
        currentOffset: monitor.getSourceClientOffset(),
    }));

    const {checkIfOutsideContainer, getTabWindowId} = useDragTab()
    const sourceWindowId = item?.windowId
    const sourceWindow = usePdfStore.getState().windows.get(sourceWindowId)
    const isFromMainTabBar = sourceWindowId === 'main';
    const isSingleTabWindow = sourceWindow && sourceWindow.tabs.length === 1;
    const shouldHideWindow = !isFromMainTabBar && isSingleTabWindow;
    let isOutsideTabBar = checkIfOutsideContainer(item, currentOffset)
    console.log("TabDragMonitor shouldHideWindow isOutsideTabBar", shouldHideWindow, isOutsideTabBar)

    useEffect(() => {
        if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB) {
            createdWindowIdRef.current = null;
            setDragTabWindowId("")
            setDragTabSourceWindowId("")
            setDragHoverWindowId("")
            return;
        }

        const windowPosition = {
            x: currentOffset.x,
            y: currentOffset.y + 49
        };

        if ((isOutsideTabBar || shouldHideWindow) && !createdWindowIdRef.current) {
            const windowId = createWindow([item.tabItem], windowPosition);
            console.log('Created window for dragged tab:', windowId);
            createdWindowIdRef.current = windowId;
            setDragTabWindowId(windowId);
            setDragTabSourceWindowId(sourceWindowId);
        }

        if (createdWindowIdRef.current) {
            updateWindow(createdWindowIdRef.current!, {
                position: windowPosition,
                zIndex: Math.floor(Date.now() / 1000)
            });
        }
    }, [isDragging, item?.id, currentOffset?.x, currentOffset?.y]);

    const dragHoverWindowId = usePdfStore.getState().dragHoverWindowId
    // 是否需要显示标签
    let shouldShowTab = isOutsideTabBar || shouldHideWindow
    if (dragHoverWindowId) {
        const dragHoverContainer = usePdfStore.getState().windowsContainer.get(dragHoverWindowId)
        shouldShowTab = checkIfOutsideContainer(item, currentOffset, dragHoverContainer)
    }

    useEffect(() => {
        setTimeout(() => {
            if (shouldShowTab && dragHoverWindowId && dragHoverWindowId !== sourceWindowId) {
                console.log("TabDragMonitor removeTab", dragHoverWindowId)
                if (dragHoverWindowId === "main") {
                    const state = usePdfStore.getState();
                    const currentTabItems = state.tabItems || [];
                    const newTabItems = currentTabItems.filter((tab) => tab?.key !== item.id);
                    setTabItems(newTabItems);

                    if (state.activeAid === item.id) {
                        const index = currentTabItems.findIndex((tab) => tab?.key === item.id);
                        if (newTabItems.length > 0) {
                            const newIndex = index === currentTabItems.length - 1 ? index - 1 : index;
                            setActiveAid(newTabItems[newIndex]?.key || '');
                        } else {
                            setActiveAid('');
                        }
                    }
                } else {
                    removeTabFromWindow(dragHoverWindowId, item.id)
                }
            }
        }, 0)
    });

    if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB) {
        return null
    }

    console.log("TabDragMonitor dragHoverWindowId shouldShowTab", dragHoverWindowId, shouldShowTab)
    return (
        <div className="bg-gray-50 border-b border border-gray-200 rounded-tl-lg rounded-tr-lg" style={{
            position: 'fixed',
            pointerEvents: 'none',
            zIndex: Math.floor(Date.now() / 1000),
            left: currentOffset.x,
            top: currentOffset.y,
            width: shouldShowTab ? '800px' : 'auto',
            height: '50px',
            // opacity: shouldShowTab ? 1 : 0,
        }}>
            <div className="flex items-center min-w-0 flex-1">
                <div className="group">
                    <div className="
    group relative flex items-center px-3 py-2 cursor-pointer select-none
    border-r border-gray-200 min-w-0 max-w-48
    transition-all duration-200 ease-in-out
    bg-white border-b-2 border-b-blue-500 text-blue-600">
                        <div className="flex items-center min-w-0 flex-1"><span
                            className="truncate text-sm font-medium">{item.tabItem.label}</span></div>
                        <button className="
        ml-2 p-1 rounded-full opacity-0 group-hover:opacity-100
        hover:bg-gray-200 transition-opacity duration-200
        opacity-70 hover:opacity-100
      ">
                            <CloseOutlined className="text-xs"/>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );

};
